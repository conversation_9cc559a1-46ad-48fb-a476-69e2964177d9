# KernelSU Next 终端功能 ADB 调试命令

## 应用信息
- **包名**: `com.rifsxd.ksunext`
- **功能**: 集成终端模拟器和二进制程序执行
- **权限**: Root 权限

## 基础 ADB 调试命令

### 1. 捕获应用所有日志
```bash
adb logcat | grep "com.rifsxd.ksunext"
```

### 2. 捕获特定标签的日志
```bash
adb logcat -s "CanaryAim:*" "TerminalSession:*" "TerminalDialog:*" "BinaryExecutor:*" "KernelSU:*"
```

### 3. 捕获应用进程的详细日志
```bash
adb logcat --pid=$(adb shell pidof com.rifsxd.ksunext)
```

### 4. 捕获系统级别相关日志
```bash
adb logcat -s "su:*" "KernelSU:*" "CanaryAim:*"
```

### 5. 实时监控终端相关日志
```bash
adb logcat | grep -E "(Terminal|Binary|CanaryAim|执行|命令|程序)"
```

### 6. 捕获错误和警告级别日志
```bash
adb logcat *:W | grep "com.rifsxd.ksunext"
```

## 高级调试命令

### 7. 捕获详细的终端会话日志
```bash
adb logcat -s "TerminalSession:V" "TerminalDialog:V" "BinaryExecutor:V"
```

### 8. 监控二进制程序执行
```bash
adb logcat | grep -E "(CanaryAim|二进制|程序|执行|启动|停止)"
```

### 9. 捕获 Root 权限相关日志
```bash
adb logcat | grep -E "(su|root|权限|Root)"
```

### 10. 实时监控文件操作
```bash
adb logcat | grep -E "(文件|目录|复制|权限|chmod)"
```

## 特定功能调试

### 终端功能调试
```bash
# 监控终端启动和命令执行
adb logcat -s "TerminalSession:*" | grep -E "(启动|命令|执行)"

# 监控终端输入输出
adb logcat -s "TerminalSession:*" | grep -E "(输入|输出|读取)"
```

### 二进制程序执行调试
```bash
# 监控 CanaryAim 程序
adb logcat -s "CanaryAim:*"

# 监控通用二进制执行器
adb logcat -s "BinaryExecutor:*"
```

### UI 交互调试
```bash
# 监控终端对话框
adb logcat -s "TerminalDialog:*"

# 监控按钮点击和用户交互
adb logcat | grep -E "(点击|按钮|对话框|用户)"
```

## 过滤和格式化

### 按时间过滤
```bash
# 最近 5 分钟的日志
adb logcat -t '05-01 12:00:00.000'

# 实时日志（从现在开始）
adb logcat -T 1
```

### 按级别过滤
```bash
# 只显示错误和警告
adb logcat *:W

# 只显示信息级别及以上
adb logcat *:I

# 显示所有级别（包括调试）
adb logcat *:V
```

### 格式化输出
```bash
# 详细格式
adb logcat -v long

# 时间格式
adb logcat -v time

# 线程格式
adb logcat -v threadtime
```

## 保存日志到文件

### 保存到本地文件
```bash
# 保存所有应用日志
adb logcat | grep "com.rifsxd.ksunext" > kernelsu_next_debug.log

# 保存终端相关日志
adb logcat -s "TerminalSession:*" "TerminalDialog:*" > terminal_debug.log

# 保存二进制执行日志
adb logcat -s "CanaryAim:*" "BinaryExecutor:*" > binary_debug.log
```

## 实用调试脚本

### 一键启动完整调试
```bash
#!/bin/bash
echo "开始 KernelSU Next 终端功能调试..."
adb logcat -c  # 清空日志缓冲区
adb logcat -s "CanaryAim:*" "TerminalSession:*" "TerminalDialog:*" "BinaryExecutor:*" "KernelSU:*" -v threadtime
```

### 监控特定操作
```bash
#!/bin/bash
echo "监控终端和二进制程序执行..."
adb logcat | grep -E "(Terminal|Binary|CanaryAim|执行|命令|程序|启动|停止)" --color=always
```

## 故障排除

### 常见问题调试
1. **终端无法启动**
   ```bash
   adb logcat -s "TerminalSession:*" | grep -E "(启动|失败|错误)"
   ```

2. **二进制程序执行失败**
   ```bash
   adb logcat -s "CanaryAim:*" "BinaryExecutor:*" | grep -E "(失败|错误|异常)"
   ```

3. **权限问题**
   ```bash
   adb logcat | grep -E "(权限|su|root|Root)" | grep -E "(失败|拒绝|错误)"
   ```

4. **文件操作问题**
   ```bash
   adb logcat | grep -E "(文件|复制|chmod|权限)" | grep -E "(失败|错误|不存在)"
   ```

## 注意事项

1. **设备要求**: 确保设备已开启 USB 调试
2. **Root 权限**: 某些功能需要 Root 权限才能正常工作
3. **日志级别**: 调试时建议使用 `*:V` 获取详细日志
4. **性能影响**: 长时间捕获详细日志可能影响设备性能
5. **存储空间**: 注意日志文件大小，及时清理

## 联系支持

如果遇到问题，请提供以下信息：
1. 设备型号和 Android 版本
2. KernelSU 版本
3. 相关的 logcat 输出
4. 复现步骤

使用上述 ADB 命令可以有效调试 KernelSU Next 的终端功能和二进制程序执行功能。
