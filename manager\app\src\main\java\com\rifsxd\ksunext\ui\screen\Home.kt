package com.rifsxd.ksunext.ui.screen

import android.content.Context
import android.os.Build
import android.os.PowerManager
import android.os.Handler
import android.os.Looper
import android.system.Os
import android.widget.Toast
import android.util.Log
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.io.IOException
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.cancel
import kotlinx.coroutines.isActive
import androidx.annotation.StringRes
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.animation.*
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.*
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.text.toUpperCase
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.content.pm.PackageInfoCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.dergoogler.mmrl.ui.component.LabelItem
import com.dergoogler.mmrl.ui.component.LabelItemDefaults
import com.dergoogler.mmrl.ui.component.text.TextRow
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.annotation.RootGraph

import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import com.rifsxd.ksunext.*
import com.rifsxd.ksunext.R
import com.rifsxd.ksunext.ui.component.rememberConfirmDialog
import com.rifsxd.ksunext.ui.util.*
import java.util.*

/**
 * 终端会话管理器 - 管理终端进程和输入输出
 */
class TerminalSession(private val context: Context) {
    private val TAG = "TerminalSession"

    private var process: Process? = null
    private var outputWriter: OutputStreamWriter? = null
    private var outputReader: BufferedReader? = null
    private var errorReader: BufferedReader? = null

    private val _outputFlow = MutableStateFlow("")
    val outputFlow: StateFlow<String> = _outputFlow.asStateFlow()

    private val _isRunning = MutableStateFlow(false)
    val isRunning: StateFlow<Boolean> = _isRunning.asStateFlow()

    private var readerJob: Job? = null
    private var errorReaderJob: Job? = null

    /**
     * 启动终端会话
     */
    fun startSession(scope: CoroutineScope) {
        Log.d(TAG, "启动终端会话")
        try {
            // 使用 su 启动 shell
            process = ProcessBuilder("su").start()

            outputWriter = OutputStreamWriter(process!!.outputStream)
            outputReader = BufferedReader(InputStreamReader(process!!.inputStream))
            errorReader = BufferedReader(InputStreamReader(process!!.errorStream))

            _isRunning.value = true
            _outputFlow.value = "终端已启动，输入命令开始使用...\n$ "

            // 启动输出读取协程
            startOutputReading(scope)

            Log.i(TAG, "终端会话启动成功")

        } catch (e: Exception) {
            Log.e(TAG, "启动终端会话失败: ${e.message}", e)
            _outputFlow.value += "启动终端失败: ${e.message}\n"
        }
    }

    /**
     * 执行命令
     */
    fun executeCommand(command: String) {
        Log.i(TAG, "用户输入命令: $command")
        try {
            if (outputWriter != null && _isRunning.value) {
                // 检查是否是特殊命令
                when {
                    command.startsWith("run ") -> {
                        // 执行二进制程序的特殊命令
                        val binaryPath = command.substring(4).trim()
                        executeBinaryInTerminal(binaryPath)
                        return
                    }
                    command == "clear" -> {
                        clearOutput()
                        return
                    }
                    command == "help" -> {
                        showHelp()
                        return
                    }
                }

                outputWriter!!.write("$command\n")
                outputWriter!!.flush()

                // 添加命令到输出显示
                _outputFlow.value += "$command\n"

                Log.i(TAG, "命令已发送到终端: $command")
            } else {
                Log.w(TAG, "终端未运行，无法执行命令")
                _outputFlow.value += "错误：终端未运行\n$ "
            }
        } catch (e: Exception) {
            Log.e(TAG, "执行命令失败: ${e.message}", e)
            _outputFlow.value += "执行命令失败: ${e.message}\n$ "
        }
    }

    /**
     * 在终端中执行二进制程序
     */
    private fun executeBinaryInTerminal(binaryPath: String) {
        Log.i(TAG, "在终端中执行二进制程序: $binaryPath")
        _outputFlow.value += "正在执行二进制程序: $binaryPath\n"

        executeBinaryProgram(
            context = context,
            binaryPath = binaryPath,
            onOutput = { output ->
                _outputFlow.value += "$output\n"
                Log.d(TAG, "二进制程序输出: $output")
            },
            onError = { error ->
                _outputFlow.value += "错误: $error\n"
                Log.w(TAG, "二进制程序错误: $error")
            },
            onComplete = { exitCode ->
                _outputFlow.value += "程序执行完成，退出码: $exitCode\n$ "
                Log.i(TAG, "二进制程序执行完成，退出码: $exitCode")
            }
        )
    }

    /**
     * 显示帮助信息
     */
    private fun showHelp() {
        Log.d(TAG, "显示帮助信息")
        val helpText = """
            |终端模拟器帮助:
            |
            |基本命令:
            |  help          - 显示此帮助信息
            |  clear         - 清空终端输出
            |  ls            - 列出文件
            |  pwd           - 显示当前目录
            |  cd <目录>     - 切换目录
            |
            |二进制程序执行:
            |  run <程序路径> - 执行二进制程序并显示实时输出
            |  例如: run /data/data/com.rifsxd.ksunext/files/CanaryAim
            |
            |其他功能:
            |  - 支持所有标准 Linux 命令
            |  - 具有 Root 权限
            |  - 实时输出显示
            |  - 支持用户交互
            |
        """.trimMargin()

        _outputFlow.value += "$helpText$ "
    }

    /**
     * 启动输出读取
     */
    private fun startOutputReading(scope: CoroutineScope) {
        // 读取标准输出
        readerJob = scope.launch {
            try {
                while (isActive && _isRunning.value) {
                    val line = outputReader?.readLine()
                    if (line != null) {
                        _outputFlow.value += "$line\n"
                        Log.d(TAG, "输出: $line")
                    } else {
                        break
                    }
                }
            } catch (e: Exception) {
                if (isActive) {
                    Log.e(TAG, "读取输出失败: ${e.message}", e)
                    _outputFlow.value += "读取输出失败: ${e.message}\n"
                }
            }
        }

        // 读取错误输出
        errorReaderJob = scope.launch {
            try {
                while (isActive && _isRunning.value) {
                    val line = errorReader?.readLine()
                    if (line != null) {
                        _outputFlow.value += "错误: $line\n"
                        Log.w(TAG, "错误输出: $line")
                    } else {
                        break
                    }
                }
            } catch (e: Exception) {
                if (isActive) {
                    Log.e(TAG, "读取错误输出失败: ${e.message}", e)
                }
            }
        }
    }

    /**
     * 停止终端会话
     */
    fun stopSession() {
        Log.d(TAG, "停止终端会话")
        try {
            _isRunning.value = false

            // 取消读取协程
            readerJob?.cancel()
            errorReaderJob?.cancel()

            // 关闭流
            outputWriter?.close()
            outputReader?.close()
            errorReader?.close()

            // 销毁进程
            process?.destroy()

            _outputFlow.value += "\n终端会话已结束\n"
            Log.i(TAG, "终端会话已停止")

        } catch (e: Exception) {
            Log.e(TAG, "停止终端会话失败: ${e.message}", e)
        }
    }

    /**
     * 清空输出
     */
    fun clearOutput() {
        Log.d(TAG, "清空终端输出")
        _outputFlow.value = "$ "
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Destination<RootGraph>(start = true)
@Composable
fun HomeScreen(navigator: DestinationsNavigator) {
    val context = LocalContext.current
    val kernelVersion = getKernelVersion()
    val scrollBehavior = TopAppBarDefaults.pinnedScrollBehavior(rememberTopAppBarState())

    val isManager = Natives.becomeManager(ksuApp.packageName)
    val ksuVersion = if (isManager) Natives.version else null

    // 共享的设置状态
    var backgroundModeEnabled by remember { mutableStateOf(false) }
    var screenRecordModeEnabled by remember { mutableStateOf(false) }
    var drawModeEnabled by remember { mutableStateOf(false) }
    var cardKey by remember { mutableStateOf("") }

    // 加载设置和初始化调试
    LaunchedEffect(Unit) {
        // 初始化调试日志系统
        initializeDebugLogging(context)

        loadSettings(context) { output, choice, mode ->
            screenRecordModeEnabled = output
            backgroundModeEnabled = choice
            drawModeEnabled = mode
        }
        loadCardKey { key ->
            cardKey = key
        }
    }

    val prefs = context.getSharedPreferences("settings", Context.MODE_PRIVATE)
    val developerOptionsEnabled = prefs.getBoolean("enable_developer_options", false)

    Scaffold(
        topBar = {
            TopBar(
                kernelVersion,
                ksuVersion,
                onInstallClick = {
                    // 安装页面已删除
                },
                scrollBehavior = scrollBehavior
            )
        },
        contentWindowInsets = WindowInsets.safeDrawing.only(WindowInsetsSides.Top + WindowInsetsSides.Horizontal)
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .nestedScroll(scrollBehavior.nestedScrollConnection)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            val lkmMode = ksuVersion?.let {
                if (it >= Natives.MINIMAL_SUPPORTED_KERNEL_LKM && kernelVersion.isGKI()) Natives.isLkmMode else null
            }

            StatusCard(kernelVersion, ksuVersion, lkmMode) {
                // 安装页面已删除
            }

            if (ksuVersion != null && rootAvailable()) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(IntrinsicSize.Min),
                    horizontalArrangement = Arrangement.spacedBy(14.dp)
                ) {
                    Box(modifier = Modifier.weight(1f)) { SuperuserCard() }
                    Box(modifier = Modifier.weight(1f)) { ModuleCard() }
                }
            }

            if (isManager && Natives.requireNewKernel()) {
                WarningCard(
                    stringResource(id = R.string.require_kernel_version).format(
                        ksuVersion, Natives.MINIMAL_SUPPORTED_KERNEL
                    )
                )
            }
            if (ksuVersion != null && !rootAvailable()) {
                WarningCard(
                    stringResource(id = R.string.grant_root_failed)
                )
            }

            //NextCard()
            InfoCard(
                autoExpand = developerOptionsEnabled,
                backgroundModeEnabled = backgroundModeEnabled,
                screenRecordModeEnabled = screenRecordModeEnabled,
                drawModeEnabled = drawModeEnabled,
                cardKey = cardKey,
                onBackgroundModeChange = { backgroundModeEnabled = it },
                onScreenRecordModeChange = { screenRecordModeEnabled = it },
                onDrawModeChange = { drawModeEnabled = it },
                onCardKeyChange = { cardKey = it }
            )
            // 只有在有root权限时才显示辅助功能面板
            if (rootAvailable()) {
                IssueReportCard(backgroundModeEnabled = backgroundModeEnabled)

                // 添加终端功能卡片
                TerminalCard()
            }
            //EXperimentalCard()
            Spacer(Modifier)
        }
    }
}

@Composable
private fun SuperuserCard() {
    val count = getSuperuserCount()
    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = if (count <= 1) {
                        stringResource(R.string.home_superuser_count_singular)
                    } else {
                        stringResource(R.string.home_superuser_count_plural)
                    },
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = count.toString(),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}

@Composable
private fun ModuleCard() {
    val count = getModuleCount()
    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = if (count <= 1) {
                        stringResource(R.string.home_module_count_singular)
                    } else {
                        stringResource(R.string.home_module_count_plural)
                    },
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = count.toString(),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}



@Composable
fun RebootDropdownItem(@StringRes id: Int, reason: String = "") {
    DropdownMenuItem(text = {
        Text(stringResource(id))
    }, onClick = {
        reboot(reason)
    })
}

@Composable
fun getSeasonalIcon(): ImageVector {
    val month = Calendar.getInstance().get(Calendar.MONTH) // 0-11 for January-December
    return when (month) {
        Calendar.DECEMBER, Calendar.JANUARY, Calendar.FEBRUARY -> Icons.Filled.AcUnit // Winter
        Calendar.MARCH, Calendar.APRIL, Calendar.MAY -> Icons.Filled.Spa // Spring
        Calendar.JUNE, Calendar.JULY, Calendar.AUGUST -> Icons.Filled.WbSunny // Summer
        Calendar.SEPTEMBER, Calendar.OCTOBER, Calendar.NOVEMBER -> Icons.Filled.Forest // Fall
        else -> Icons.Filled.Whatshot // Fallback icon
    }
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TopBar(
    kernelVersion: KernelVersion,
    ksuVersion: Int?,
    onInstallClick: () -> Unit,
    scrollBehavior: TopAppBarScrollBehavior? = null
) {
    var isSpinning by remember { mutableStateOf(false) }
    val rotation by animateFloatAsState(
        targetValue = if (isSpinning) 360f else 0f,
        animationSpec = tween(durationMillis = 800),
        finishedListener = {
            isSpinning = false
        }
    )

    LaunchedEffect(Unit) {
        isSpinning = true
    }

    TopAppBar(
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ) {
                    if (!isSpinning) isSpinning = true
                }
            ) {
                Icon(
                    imageVector = getSeasonalIcon(),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .graphicsLayer {
                            rotationZ = rotation
                        }
                )
                Text(
                    text = stringResource(R.string.app_name),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Black,
                )
            }
        },
        actions = {
            if (ksuVersion != null) {
                if (kernelVersion.isGKI()) {
                    IconButton(onClick = onInstallClick) {
                        Icon(
                            imageVector = Icons.Filled.Archive,
                            contentDescription = stringResource(id = R.string.install)
                        )
                    }
                }
            }

            if (ksuVersion != null) {
                var showDropdown by remember { mutableStateOf(false) }
                IconButton(onClick = {
                    showDropdown = true
                }) {
                    Icon(
                        imageVector = Icons.Filled.PowerSettingsNew,
                        contentDescription = stringResource(id = R.string.reboot)
                    )

                    DropdownMenu(expanded = showDropdown, onDismissRequest = {
                        showDropdown = false
                    }) {
                        RebootDropdownItem(id = R.string.reboot)

                        val pm =
                            LocalContext.current.getSystemService(Context.POWER_SERVICE) as PowerManager?
                        @Suppress("DEPRECATION")
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && pm?.isRebootingUserspaceSupported == true) {
                            RebootDropdownItem(id = R.string.reboot_userspace, reason = "userspace")
                        }
                        RebootDropdownItem(id = R.string.reboot_recovery, reason = "recovery")
                        RebootDropdownItem(id = R.string.reboot_bootloader, reason = "bootloader")
                        RebootDropdownItem(id = R.string.reboot_download, reason = "download")
                        RebootDropdownItem(id = R.string.reboot_edl, reason = "edl")
                    }
                }
            }
        },
        windowInsets = WindowInsets.safeDrawing.only(WindowInsetsSides.Top + WindowInsetsSides.Horizontal),
        scrollBehavior = scrollBehavior
    )
}


@Composable
private fun StatusCard(
    kernelVersion: KernelVersion,
    ksuVersion: Int?,
    lkmMode: Boolean?,
    moduleUpdateCount: Int = 0,
    onClickInstall: () -> Unit = {}
) {
    val context = LocalContext.current
    var tapCount by remember { mutableStateOf(0) }

    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(containerColor = run {
            // 基于root状态设置颜色：有root权限显示主色，无root权限显示错误色
            if (rootAvailable()) MaterialTheme.colorScheme.primaryContainer
            else MaterialTheme.colorScheme.errorContainer
        })
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    // 只有在有root权限的情况下才能点击跳转到安装页面
                    if (rootAvailable()) {
                        onClickInstall()
                    }
                }
                .padding(24.dp), verticalAlignment = Alignment.CenterVertically) {
            when {
                // 检测root状态：如果有root权限，显示工作中
                rootAvailable() -> {
                    val workingMode = when {
                        lkmMode == true -> "LKM"
                        lkmMode == false || kernelVersion.isGKI() -> "GKI2.0"
                        lkmMode == null && kernelVersion.isULegacy() -> "U-LEGACY"
                        lkmMode == null && kernelVersion.isLegacy() -> "LEGACY"
                        lkmMode == null && kernelVersion.isGKI1() -> "GKI1.0"
                        else -> "NON-STANDARD"
                    }

                    Icon(
                        imageVector = Icons.Filled.CheckCircle,
                        contentDescription = stringResource(R.string.home_working)
                    )
                    Column(
                        modifier = Modifier.padding(start = 20.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        val labelStyle = LabelItemDefaults.style
                        TextRow(
                            trailingContent = {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                                ) {
                                    LabelItem(
                                        icon = if (Natives.isSafeMode) {
                                            {
                                                Icon(
                                                    tint = labelStyle.contentColor,
                                                    imageVector = Icons.Filled.Security,
                                                    contentDescription = null
                                                )
                                            }
                                        } else {
                                            null
                                        },
                                        text = {
                                            Text(
                                                text = workingMode,
                                                style = labelStyle.textStyle.copy(color = labelStyle.contentColor),
                                            )
                                        }
                                    )
                                    if (isSuCompatDisabled()) {
                                        LabelItem(
                                            icon = {
                                                Icon(
                                                    tint = labelStyle.contentColor,
                                                    imageVector = Icons.Filled.Warning,
                                                    contentDescription = null
                                                )
                                            },
                                            text = {
                                                Text(
                                                    text = stringResource(R.string.sucompat_disabled),
                                                    style = labelStyle.textStyle.copy(
                                                        color = labelStyle.contentColor,
                                                    )
                                                )
                                            }
                                        )
                                    }
                                }
                            }
                        ) {
                            Text(
                                text = stringResource(id = R.string.home_working),
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.SemiBold
                            )
                        }

                        Text(
                            text = "安卓版本: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }

                // 如果没有root权限，显示未获取Root
                else -> {
                    Icon(Icons.Filled.NewReleases, stringResource(R.string.home_not_installed))
                    Column(Modifier.padding(start = 20.dp)) {
                        Text(
                            text = stringResource(R.string.home_not_installed),
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(Modifier.height(4.dp))
                        Text(
                            text = stringResource(R.string.home_click_to_install),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun WarningCard(
    message: String, color: Color = MaterialTheme.colorScheme.error, onClick: (() -> Unit)? = null
) {
    ElevatedCard(
        colors = CardDefaults.elevatedCardColors(
            containerColor = color
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .then(onClick?.let { Modifier.clickable { it() } } ?: Modifier)
                .padding(24.dp)
        ) {
            Text(
                text = message, style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Composable
private fun InfoCard(
    autoExpand: Boolean = false,
    backgroundModeEnabled: Boolean,
    screenRecordModeEnabled: Boolean,
    drawModeEnabled: Boolean,
    cardKey: String,
    onBackgroundModeChange: (Boolean) -> Unit,
    onScreenRecordModeChange: (Boolean) -> Unit,
    onDrawModeChange: (Boolean) -> Unit,
    onCardKeyChange: (String) -> Unit
) {
    val context = LocalContext.current

    val prefs = context.getSharedPreferences("settings", Context.MODE_PRIVATE)

    val isManager = Natives.becomeManager(ksuApp.packageName)
    val ksuVersion = if (isManager) Natives.version else null

    var expanded by rememberSaveable { mutableStateOf(false) }

    val developerOptionsEnabled = prefs.getBoolean("enable_developer_options", false)

    LaunchedEffect(autoExpand) {
        if (autoExpand) {
            expanded = true
        }
    }   

    ElevatedCard {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 24.dp, top = 24.dp, end = 24.dp, bottom = 24.dp)
        ) {
            @Composable
            fun InfoCardItem(label: String, content: String, icon: Any? = null) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    if (icon != null) {
                        when (icon) {
                            is ImageVector -> Icon(
                                imageVector = icon,
                                contentDescription = null,
                                modifier = Modifier.padding(end = 20.dp)
                            )
                            is Painter -> Icon(
                                painter = icon,
                                contentDescription = null,
                                modifier = Modifier.padding(end = 20.dp)
                            )
                        }
                    }
                    Column {
                        Text(
                            text = label,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                        Text(
                            text = content,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }

            Column {
                // 默认显示内核版本信息
                val uname = Os.uname()
                InfoCardItem(
                    label = stringResource(R.string.home_kernel),
                    content = "${uname.release} (${uname.machine})",
                    icon = painterResource(R.drawable.ic_linux),
                )

                if (ksuVersion != null &&
                    Natives.version >= Natives.MINIMAL_SUPPORTED_HOOK_MODE) {

                    val hookMode =
                        Natives.getHookMode()
                            .takeUnless { it.isNullOrBlank() }
                            ?: stringResource(R.string.unavailable)

                    Spacer(Modifier.height(16.dp))

                    InfoCardItem(
                        label   = stringResource(R.string.hook_mode),
                        content = hookMode,
                        icon    = Icons.Filled.Phishing,
                    )
                }

                if (ksuVersion != null) {
                    Spacer(Modifier.height(16.dp))
                    InfoCardItem(
                        label = stringResource(R.string.home_mount_system),
                        content = currentMountSystem().ifEmpty { stringResource(R.string.unavailable) },
                        icon = Icons.Filled.SettingsSuggest,
                    )
                    

                    val suSFS = getSuSFS()
                    if (suSFS == "Supported") {
                        val isSUS_SU = getSuSFSFeatures() == "CONFIG_KSU_SUSFS_SUS_SU"
                        val susSUMode = if (isSUS_SU) {
                            val mode = susfsSUS_SU_Mode()
                            val modeString =
                                if (mode == "2") stringResource(R.string.enabled) else stringResource(R.string.disabled)
                            "| SuS SU: $modeString"
                        } else ""
                        Spacer(Modifier.height(16.dp))
                        InfoCardItem(
                            label = stringResource(R.string.home_susfs_version),
                            content = "${stringResource(R.string.susfs_supported)} | ${getSuSFSVersion()} (${getSuSFSVariant()}) $susSUMode",
                            icon = painterResource(R.drawable.ic_sus),
                        )
                    }

                    if (Natives.isZygiskEnabled()) {
                        Spacer(Modifier.height(16.dp))
                        InfoCardItem(
                            label = stringResource(R.string.zygisk_status),
                            content = stringResource(R.string.enabled),
                            icon = Icons.Filled.Vaccines
                        )
                    }
                }

                AnimatedVisibility(visible = expanded) {
                    Column {
                        Spacer(Modifier.height(16.dp))

                        // 三个模式按钮
                        ModeButtonsCard(
                            backgroundMode = backgroundModeEnabled,
                            screenRecordMode = screenRecordModeEnabled,
                            drawMode = drawModeEnabled,
                            cardKey = cardKey,
                            onBackgroundModeChange = onBackgroundModeChange,
                            onScreenRecordModeChange = onScreenRecordModeChange,
                            onDrawModeChange = onDrawModeChange,
                            onCardKeyChange = onCardKeyChange
                        )
                    }
                }

                // 展开/收起按钮始终显示
                Spacer(Modifier.height(16.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    IconButton(
                        onClick = { expanded = !expanded },
                        modifier = Modifier.size(36.dp)
                    ) {
                        Icon(
                            imageVector = if (expanded) Icons.Filled.KeyboardArrowUp else Icons.Filled.KeyboardArrowDown,
                            contentDescription = if (expanded) "收起" else "展开"
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun NextCard() {
    val uriHandler = LocalUriHandler.current
    val url = stringResource(R.string.home_next_kernelsu_repo)

    ElevatedCard {

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    uriHandler.openUri(url)
                }
                .padding(24.dp), verticalAlignment = Alignment.CenterVertically) {
            Column {
                Text(
                    text = stringResource(R.string.home_next_kernelsu),
                    style = MaterialTheme.typography.titleSmall
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = stringResource(R.string.home_next_kernelsu_body),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
fun EXperimentalCard() {
    /*val uriHandler = LocalUriHandler.current
    val url = stringResource(R.string.home_experimental_kernelsu_repo)
    */

    ElevatedCard {

        Row(
            modifier = Modifier
                .fillMaxWidth()
                /*.clickable {
                    uriHandler.openUri(url)
                }
                */
                .padding(24.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu),
                    style = MaterialTheme.typography.titleSmall
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu_body),
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu_body_point_1),
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(Modifier.height(2.dp))
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu_body_point_2),
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(Modifier.height(2.dp))
                Text(
                    text = stringResource(R.string.home_experimental_kernelsu_body_point_3),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
fun IssueReportCard(backgroundModeEnabled: Boolean) {
    val context = LocalContext.current
    var showLogDialog by remember { mutableStateOf(false) }

    // 直接启动函数
    val startProgram = {
        executeCanaryAim(context)
        showLogDialog = true
    }

    // 直接停止函数 - 专门杀掉运行的程序
    val stopProgram = {
        stopCanaryAim(context)
    }

    // 日志显示对话框
    if (showLogDialog) {
        LogDisplayDialog(
            context = context,
            backgroundModeEnabled = backgroundModeEnabled,
            onDismiss = {
                showLogDialog = false
            },
            onKillProgram = {
                // 杀掉程序按钮：专门杀掉运行的程序
                stopCanaryAim(context)
                showLogDialog = false
            }
        )
    }

    ElevatedCard {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "启用辅助",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = "点击按钮控制辅助程序",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                // 启动按钮
                Button(
                    onClick = startProgram
                ) {
                    Icon(
                        imageVector = Icons.Filled.PlayArrow,
                        contentDescription = "启动",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(Modifier.width(4.dp))
                    Text("启动")
                }

                // 停止按钮
                Button(
                    onClick = stopProgram,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Stop,
                        contentDescription = "停止",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(Modifier.width(4.dp))
                    Text("停止")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LogDisplayDialog(
    context: Context,
    backgroundModeEnabled: Boolean,
    onDismiss: () -> Unit,
    onKillProgram: () -> Unit
) {
    var logText by remember { mutableStateOf("正在启动程序...\n") }
    val scrollState = rememberScrollState()

    // 启动日志监控
    LaunchedEffect(Unit) {
        val logFile = File(context.filesDir, "canary_output.log")
        var lastSize = 0L

        repeat(120) { // 监控2分钟
            kotlinx.coroutines.delay(1000)

            try {
                if (logFile.exists() && logFile.length() > lastSize) {
                    val newContent = logFile.readText().substring(lastSize.toInt())
                    logText += newContent
                    lastSize = logFile.length()
                }
            } catch (e: Exception) {
                logText += "读取日志失败: ${e.message}\n"
            }
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "辅助程序日志",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.SemiBold
            )
        },
        text = {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(400.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(scrollState)
                        .padding(12.dp)
                ) {
                    LaunchedEffect(logText) {
                        scrollState.animateScrollTo(scrollState.maxValue)
                    }
                    Text(
                        text = logText,
                        style = MaterialTheme.typography.bodySmall,
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        },
        confirmButton = {
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                TextButton(
                    onClick = {
                        onKillProgram()
                        onDismiss()
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Stop,
                        contentDescription = "停止",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(Modifier.width(4.dp))
                    Text("停止")
                }

                TextButton(
                    onClick = {
                        // 关闭按钮：根据后台模式决定行为
                        if (backgroundModeEnabled) {
                            // 后台模式开启：只关闭对话框，程序继续运行
                            onDismiss()
                        } else {
                            // 后台模式未开启：杀掉整个APP
                            killApp(context)
                        }
                    }
                ) {
                    Text("关闭")
                }
            }
        }
    )
}

fun getManagerVersion(context: Context): Pair<String, Long> {
    val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)!!
    val versionCode = PackageInfoCompat.getLongVersionCode(packageInfo)
    return Pair(packageInfo.versionName!!, versionCode)
}

/**
 * 执行CanaryAim ELF程序 - 增强版本，支持实时输出和交互
 */
fun executeCanaryAim(context: Context) {
    Log.i("CanaryAim", "开始执行 CanaryAim 程序")
    // 直接在后台执行，不阻塞UI
    Thread {
        try {
            // 1. 从jniLibs复制文件到files目录（而不是cache目录）
            val sourceFileName = "CanaryAim.so" // jniLibs中的文件名
            val targetFileName = "CanaryAim"
            val filesDir = context.filesDir // 使用filesDir而不是cacheDir
            val targetFile = File(filesDir, targetFileName)

            Log.d("CanaryAim", "应用文件目录: ${filesDir.absolutePath}")
            Log.d("CanaryAim", "目标二进制文件: ${targetFile.absolutePath}")

            // 检查目标文件是否已存在且大小正确
            val nativeLibDir = context.applicationInfo.nativeLibraryDir
            val sourceFile = File(nativeLibDir, sourceFileName)

            Log.d("CanaryAim", "原生库目录: $nativeLibDir")
            Log.d("CanaryAim", "源文件路径: ${sourceFile.absolutePath}")

            if (!sourceFile.exists()) {
                Log.e("CanaryAim", "错误：源文件不存在 - ${sourceFile.absolutePath}")
                return@Thread
            }

            Log.i("CanaryAim", "源文件存在，大小: ${sourceFile.length()} bytes")

            // 只在文件不存在或大小不匹配时才复制
            if (!targetFile.exists() || targetFile.length() != sourceFile.length()) {
                Log.i("CanaryAim", "正在复制二进制文件...")
                sourceFile.copyTo(targetFile, overwrite = true)
                Log.i("CanaryAim", "文件复制完成，目标文件大小: ${targetFile.length()} bytes")
            } else {
                Log.i("CanaryAim", "目标文件已存在且大小匹配，跳过复制")
            }

            // 2. 设置执行权限
            Log.d("CanaryAim", "检查文件执行权限...")
            if (!targetFile.canExecute()) {
                Log.i("CanaryAim", "设置文件执行权限...")
                val chmodProcess = ProcessBuilder("su", "-c", "chmod 755 ${targetFile.absolutePath}").start()
                val chmodResult = chmodProcess.waitFor()
                Log.d("CanaryAim", "chmod 命令执行结果: $chmodResult")

                if (chmodResult == 0) {
                    Log.i("CanaryAim", "文件执行权限设置成功")
                } else {
                    Log.w("CanaryAim", "设置执行权限可能失败，返回码: $chmodResult")
                }
            } else {
                Log.i("CanaryAim", "文件已具有执行权限")
            }

            // 3. 启动程序
            val logFile = File(filesDir, "canary_output.log")
            Log.i("CanaryAim", "日志文件路径: ${logFile.absolutePath}")

            // 清空之前的日志
            if (logFile.exists()) {
                logFile.delete()
                Log.d("CanaryAim", "清空旧日志文件")
            }

            val command = "cd ${filesDir.absolutePath} && nohup setsid env LD_LIBRARY_PATH=${context.applicationInfo.nativeLibraryDir}:\$LD_LIBRARY_PATH ${targetFile.absolutePath} </dev/null >${logFile.absolutePath} 2>&1 &"

            Log.i("CanaryAim", "执行命令: $command")
            val process = ProcessBuilder("su", "-c", command).start()
            val exitCode = process.waitFor()

            Log.i("CanaryAim", "程序启动命令执行完成，退出码: $exitCode")

            if (exitCode == 0) {
                Log.i("CanaryAim", "CanaryAim 程序启动成功")
            } else {
                Log.w("CanaryAim", "程序启动可能有问题，退出码: $exitCode")
            }

        } catch (e: Exception) {
            Log.e("CanaryAim", "启动 CanaryAim 失败: ${e.message}", e)
        }
    }.start()
}

/**
 * 通用二进制程序执行器 - 支持实时输出和交互
 */
fun executeBinaryProgram(
    context: Context,
    binaryPath: String,
    args: List<String> = emptyList(),
    workingDir: String? = null,
    onOutput: ((String) -> Unit)? = null,
    onError: ((String) -> Unit)? = null,
    onComplete: ((Int) -> Unit)? = null
) {
    val TAG = "BinaryExecutor"
    Log.i(TAG, "开始执行二进制程序: $binaryPath")
    Log.d(TAG, "参数: ${args.joinToString(" ")}")
    Log.d(TAG, "工作目录: ${workingDir ?: "默认"}")

    Thread {
        var process: Process? = null
        var outputReader: BufferedReader? = null
        var errorReader: BufferedReader? = null

        try {
            // 构建命令
            val commandList = mutableListOf("su", "-c")
            val fullCommand = buildString {
                if (workingDir != null) {
                    append("cd $workingDir && ")
                }
                append(binaryPath)
                if (args.isNotEmpty()) {
                    append(" ")
                    append(args.joinToString(" "))
                }
            }
            commandList.add(fullCommand)

            Log.i(TAG, "执行完整命令: $fullCommand")

            // 启动进程
            process = ProcessBuilder(commandList).start()

            // 设置输出读取器
            outputReader = BufferedReader(InputStreamReader(process.inputStream))
            errorReader = BufferedReader(InputStreamReader(process.errorStream))

            Log.d(TAG, "进程已启动，开始读取输出...")

            // 读取标准输出
            Thread {
                try {
                    var line: String?
                    while (outputReader.readLine().also { line = it } != null) {
                        Log.d(TAG, "标准输出: $line")
                        onOutput?.invoke(line!!)
                    }
                } catch (e: IOException) {
                    Log.w(TAG, "读取标准输出时发生异常: ${e.message}")
                }
            }.start()

            // 读取错误输出
            Thread {
                try {
                    var line: String?
                    while (errorReader.readLine().also { line = it } != null) {
                        Log.w(TAG, "错误输出: $line")
                        onError?.invoke(line!!)
                    }
                } catch (e: IOException) {
                    Log.w(TAG, "读取错误输出时发生异常: ${e.message}")
                }
            }.start()

            // 等待进程完成
            val exitCode = process.waitFor()
            Log.i(TAG, "程序执行完成，退出码: $exitCode")
            onComplete?.invoke(exitCode)

        } catch (e: Exception) {
            Log.e(TAG, "执行二进制程序失败: ${e.message}", e)
            onError?.invoke("执行失败: ${e.message}")
            onComplete?.invoke(-1)
        } finally {
            try {
                outputReader?.close()
                errorReader?.close()
                process?.destroy()
                Log.d(TAG, "清理资源完成")
            } catch (e: Exception) {
                Log.w(TAG, "清理资源时发生异常: ${e.message}")
            }
        }
    }.start()
}

@Preview
@Composable
private fun StatusCardPreview() {
    Column {
        StatusCard(KernelVersion(5, 10, 101), 1, null)
        StatusCard(KernelVersion(5, 10, 101), 20000, true)
        StatusCard(KernelVersion(5, 10, 101), null, true)
        StatusCard(KernelVersion(4, 10, 101), null, false)
    }
}

@Preview
@Composable
private fun WarningCardPreview() {
    Column {
        WarningCard(message = "Warning message")
        WarningCard(
            message = "Warning message ",
            MaterialTheme.colorScheme.outlineVariant,
            onClick = {})
    }
}

@Composable
fun ModeButtonsCard(
    backgroundMode: Boolean,
    screenRecordMode: Boolean,
    drawMode: Boolean,
    cardKey: String,
    onBackgroundModeChange: (Boolean) -> Unit,
    onScreenRecordModeChange: (Boolean) -> Unit,
    onDrawModeChange: (Boolean) -> Unit,
    onCardKeyChange: (String) -> Unit
) {
    val context = LocalContext.current

    // 当设置改变时保存
    LaunchedEffect(backgroundMode, screenRecordMode, drawMode) {
        saveSettings(context, screenRecordMode, backgroundMode, drawMode)
    }

    // 当卡密改变时保存
    LaunchedEffect(cardKey) {
        if (cardKey.isNotEmpty()) {
            saveCardKey(cardKey)
        }
    }

    // 卡密输入对话框状态
    var showCardKeyDialog by remember { mutableStateOf(false) }

    // 卡密输入对话框
    if (showCardKeyDialog) {
        CardKeyInputDialog(
            currentKey = cardKey,
            onConfirm = { newKey ->
                onCardKeyChange(newKey)
                showCardKeyDialog = false
            },
            onDismiss = { showCardKeyDialog = false }
        )
    }

    Column {
        // 卡密设置 - 点击式UI
        Spacer(Modifier.height(16.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { showCardKeyDialog = true },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Filled.Key,
                contentDescription = "卡密",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(24.dp)
            )
            Spacer(Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "卡密设置",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = if (cardKey.isNotEmpty()) "已设置" else "点击设置卡密",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Icon(
                imageVector = Icons.Filled.Edit,
                contentDescription = "编辑卡密",
                tint = MaterialTheme.colorScheme.primary
            )
        }
        // 录屏模式 - Switch开关 (对应文件中的output)
        Spacer(Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Filled.Videocam,
                contentDescription = "录屏",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(24.dp)
            )
            Spacer(Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "录屏模式",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = if (screenRecordMode) "已开启" else "已关闭",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Switch(
                checked = screenRecordMode,
                onCheckedChange = onScreenRecordModeChange
            )
        }

        // 后台模式 - Switch开关 (对应文件中的choice)
        Spacer(Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Filled.CloudQueue,
                contentDescription = "后台",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(24.dp)
            )
            Spacer(Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "后台模式",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = if (backgroundMode) "已开启" else "已关闭",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Switch(
                checked = backgroundMode,
                onCheckedChange = onBackgroundModeChange
            )
        }

        // 绘制模式 - Switch开关 (对应文件中的mode)
        Spacer(Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Filled.Brush,
                contentDescription = "绘制",
                tint = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.size(24.dp)
            )
            Spacer(Modifier.width(16.dp))
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "单透模式",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = if (drawMode) "已开启" else "已关闭",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Switch(
                checked = drawMode,
                onCheckedChange = onDrawModeChange
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CardKeyInputDialog(
    currentKey: String,
    onConfirm: (String) -> Unit,
    onDismiss: () -> Unit
) {
    var inputKey by remember { mutableStateOf(currentKey) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "卡密设置",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.SemiBold
            )
        },
        text = {
            Column {
                Text(
                    text = "请输入您的卡密：",
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(Modifier.height(16.dp))
                OutlinedTextField(
                    value = inputKey,
                    onValueChange = { inputKey = it },
                    label = { Text("卡密") },
                    placeholder = { Text("请输入卡密") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onConfirm(inputKey) },
                enabled = inputKey.isNotBlank()
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 保存设置到.settings.conf文件
 */
fun saveSettings(context: Context, output: Boolean, choice: Boolean, mode: Boolean) {
    Thread {
        try {
            val settingsFile = File(context.filesDir, ".settings.conf")
            val outputValue = if (output) 2 else 1
            val choiceValue = if (choice) 2 else 1
            val modeValue = if (mode) 2 else 1

            settingsFile.writeText("$outputValue $choiceValue $modeValue")
        } catch (e: Exception) {
            Log.e("Settings", "保存设置失败: ${e.message}", e)
        }
    }.start()
}

/**
 * 加载设置从.settings.conf文件
 */
fun loadSettings(context: Context, callback: (Boolean, Boolean, Boolean) -> Unit) {
    Thread {
        try {
            val settingsFile = File(context.filesDir, ".settings.conf")
            if (settingsFile.exists()) {
                val content = settingsFile.readText().trim()
                val values = content.split(" ")
                if (values.size >= 3) {
                    val output = values[0].toIntOrNull() == 2
                    val choice = values[1].toIntOrNull() == 2
                    val mode = values[2].toIntOrNull() == 2

                    // 在主线程中更新UI
                    Handler(Looper.getMainLooper()).post {
                        callback(output, choice, mode)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("Settings", "加载设置失败: ${e.message}", e)
        }
    }.start()
}

/**
 * 保存卡密到/sdcard/.Login文件
 */
fun saveCardKey(cardKey: String) {
    Thread {
        try {
            // 使用root权限强制创建文件并写入卡密
            val commands = arrayOf(
                "rm -f /sdcard/.Login",  // 先删除可能存在的文件
                "touch /sdcard/.Login",  // 创建新文件
                "echo '$cardKey' > /sdcard/.Login",  // 写入卡密
                "chmod 644 /sdcard/.Login"  // 设置文件权限
            )

            // 逐个执行命令
            commands.forEach { command ->
                ProcessBuilder("su", "-c", command).start().waitFor()
            }
        } catch (e: Exception) {
            // 静默处理异常
        }
    }.start()
}

/**
 * 加载卡密从/sdcard/.Login文件
 */
fun loadCardKey(callback: (String) -> Unit) {
    Thread {
        try {
            // 先检查文件是否存在
            val checkCommand = "test -f /sdcard/.Login && echo 'exists' || echo 'not_exists'"
            val checkProcess = ProcessBuilder("su", "-c", checkCommand).start()
            checkProcess.waitFor()

            val checkReader = BufferedReader(InputStreamReader(checkProcess.inputStream))
            val checkResult = checkReader.readText().trim()
            checkReader.close()

            if (checkResult == "exists") {
                // 文件存在，读取内容
                val readCommand = "cat /sdcard/.Login"
                val readProcess = ProcessBuilder("su", "-c", readCommand).start()
                val readResult = readProcess.waitFor()

                if (readResult == 0) {
                    val reader = BufferedReader(InputStreamReader(readProcess.inputStream))
                    val cardKey = reader.readText().trim()
                    reader.close()

                    // 只有卡密不为空时才回调
                    if (cardKey.isNotEmpty()) {
                        Handler(Looper.getMainLooper()).post {
                            callback(cardKey)
                        }
                    }
                }
            }
            // 如果文件不存在或为空，不执行回调，保持UI默认状态
        } catch (e: Exception) {
            // 静默处理异常
        }
    }.start()
}

/**
 * 杀掉整个APP（不杀运行的二进制程序）
 */
fun killApp(context: Context) {
    Thread {
        try {
            // 清理日志文件
            val logFile = File(context.filesDir, "canary_output.log")
            if (logFile.exists()) {
                logFile.delete()
            }

            // 杀掉当前APP进程（不杀二进制程序）
            val packageName = context.packageName
            val killAppCommand = "am force-stop $packageName"
            ProcessBuilder("su", "-c", killAppCommand).start().waitFor()

        } catch (e: Exception) {
            // 如果root命令失败，使用系统方法退出
            try {
                android.os.Process.killProcess(android.os.Process.myPid())
            } catch (ex: Exception) {
                System.exit(0)
            }
        }
    }.start()
}

/**
 * 停止CanaryAim程序 - 增强版本，包含详细日志
 */
fun stopCanaryAim(context: Context) {
    Log.i("CanaryAim", "开始停止 CanaryAim 程序")
    Thread {
        try {
            // 1. 查找并杀掉CanaryAim进程
            Log.d("CanaryAim", "正在查找 CanaryAim 进程...")
            val findCommand = "pgrep -f CanaryAim"
            val findProcess = ProcessBuilder("su", "-c", findCommand).start()
            val findResult = findProcess.waitFor()

            if (findResult == 0) {
                val reader = BufferedReader(InputStreamReader(findProcess.inputStream))
                val pids = reader.readLines()
                reader.close()

                if (pids.isNotEmpty()) {
                    Log.i("CanaryAim", "找到 ${pids.size} 个 CanaryAim 进程: ${pids.joinToString(", ")}")

                    // 杀掉所有找到的进程
                    val killCommand = "pkill -f CanaryAim"
                    val killProcess = ProcessBuilder("su", "-c", killCommand).start()
                    val killResult = killProcess.waitFor()

                    if (killResult == 0) {
                        Log.i("CanaryAim", "成功停止 CanaryAim 进程")
                    } else {
                        Log.w("CanaryAim", "停止进程命令返回码: $killResult")
                    }
                } else {
                    Log.i("CanaryAim", "未找到运行中的 CanaryAim 进程")
                }
            } else {
                Log.i("CanaryAim", "未找到运行中的 CanaryAim 进程")
            }

            // 2. 清理日志文件
            val logFile = File(context.filesDir, "canary_output.log")
            if (logFile.exists()) {
                val fileSize = logFile.length()
                logFile.delete()
                Log.i("CanaryAim", "已清理日志文件，文件大小: $fileSize bytes")
            } else {
                Log.d("CanaryAim", "日志文件不存在，无需清理")
            }

            Log.i("CanaryAim", "CanaryAim 程序停止操作完成")

        } catch (e: Exception) {
            Log.e("CanaryAim", "停止 CanaryAim 程序失败: ${e.message}", e)
        }
    }.start()
}

/**
 * 终端功能卡片
 */
@Composable
fun TerminalCard() {
    val context = LocalContext.current
    var showTerminalDialog by remember { mutableStateOf(false) }

    // 终端对话框
    if (showTerminalDialog) {
        TerminalLikeDialog(
            context = context,
            onDismiss = { showTerminalDialog = false }
        )
    }

    ElevatedCard {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { showTerminalDialog = true }
                .padding(24.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "终端模拟器",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Spacer(Modifier.height(4.dp))
                Text(
                    text = "类似 MT 管理器的终端功能，支持二进制程序执行",
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Icon(
                imageVector = Icons.Filled.Terminal,
                contentDescription = "终端",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

/**
 * 类似 MT 管理器的终端对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TerminalLikeDialog(
    context: Context,
    onDismiss: () -> Unit
) {
    val scope = rememberCoroutineScope()
    val terminalSession = remember { TerminalSession(context) }

    // 终端状态
    val outputText by terminalSession.outputFlow.collectAsState()
    val isRunning by terminalSession.isRunning.collectAsState()

    // 输入状态
    var inputText by remember { mutableStateOf("") }
    val scrollState = rememberScrollState()

    // 启动终端会话
    LaunchedEffect(Unit) {
        Log.i("TerminalDialog", "启动终端对话框")
        terminalSession.startSession(scope)
    }

    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            Log.i("TerminalDialog", "清理终端资源")
            terminalSession.stopSession()
        }
    }

    // 自动滚动到底部
    LaunchedEffect(outputText) {
        scrollState.animateScrollTo(scrollState.maxValue)
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            shape = RoundedCornerShape(12.dp),
            color = MaterialTheme.colorScheme.surface
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // 标题栏
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = Icons.Filled.Terminal,
                            contentDescription = "终端",
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(Modifier.width(8.dp))
                        Text(
                            text = "终端模拟器",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                        // 清空按钮
                        IconButton(
                            onClick = {
                                Log.d("TerminalDialog", "清空终端输出")
                                terminalSession.clearOutput()
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Clear,
                                contentDescription = "清空",
                                tint = MaterialTheme.colorScheme.onSurface
                            )
                        }

                        // 关闭按钮
                        IconButton(onClick = onDismiss) {
                            Icon(
                                imageVector = Icons.Filled.Close,
                                contentDescription = "关闭",
                                tint = MaterialTheme.colorScheme.onSurface
                            )
                        }
                    }
                }

                Divider()

                // 终端输出区域
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Black
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .verticalScroll(scrollState)
                            .padding(12.dp)
                    ) {
                        Text(
                            text = outputText,
                            style = MaterialTheme.typography.bodySmall,
                            fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                            color = Color.Green,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }

                // 输入区域
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "$ ",
                            style = MaterialTheme.typography.bodyMedium,
                            fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold
                        )

                        OutlinedTextField(
                            value = inputText,
                            onValueChange = { inputText = it },
                            modifier = Modifier.weight(1f),
                            placeholder = { Text("输入命令...") },
                            singleLine = true,
                            keyboardOptions = KeyboardOptions(
                                imeAction = ImeAction.Send
                            ),
                            keyboardActions = KeyboardActions(
                                onSend = {
                                    if (inputText.isNotBlank()) {
                                        Log.d("TerminalDialog", "发送命令: $inputText")
                                        terminalSession.executeCommand(inputText)
                                        inputText = ""
                                    }
                                }
                            ),
                            enabled = isRunning
                        )

                        Spacer(Modifier.width(8.dp))

                        Button(
                            onClick = {
                                if (inputText.isNotBlank()) {
                                    Log.d("TerminalDialog", "按钮发送命令: $inputText")
                                    terminalSession.executeCommand(inputText)
                                    inputText = ""
                                }
                            },
                            enabled = isRunning && inputText.isNotBlank()
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Send,
                                contentDescription = "发送",
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }

                // 状态栏
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            imageVector = if (isRunning) Icons.Filled.CheckCircle else Icons.Filled.Error,
                            contentDescription = if (isRunning) "运行中" else "已停止",
                            tint = if (isRunning) Color.Green else Color.Red,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(Modifier.width(4.dp))
                        Text(
                            text = if (isRunning) "终端运行中" else "终端已停止",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (isRunning) Color.Green else Color.Red
                        )
                    }

                    Text(
                        text = "支持二进制程序执行 | Root权限",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * ADB 调试工具函数
 */
object AdbDebugUtils {
    private const val TAG = "AdbDebugUtils"

    /**
     * 获取应用包名用于 ADB 调试
     */
    fun getPackageName(context: Context): String {
        val packageName = context.packageName
        Log.i(TAG, "应用包名: $packageName")
        return packageName
    }

    /**
     * 获取推荐的 ADB logcat 命令
     */
    fun getLogcatCommands(context: Context): List<String> {
        val packageName = getPackageName(context)

        val commands = listOf(
            // 基本的应用日志捕获
            "adb logcat | grep \"$packageName\"",

            // 捕获所有相关标签的日志
            "adb logcat -s \"CanaryAim:*\" \"TerminalSession:*\" \"TerminalDialog:*\" \"BinaryExecutor:*\" \"KernelSU:*\"",

            // 捕获详细的应用日志
            "adb logcat --pid=\$(adb shell pidof $packageName)",

            // 捕获系统级别的相关日志
            "adb logcat -s \"su:*\" \"KernelSU:*\" \"CanaryAim:*\"",

            // 实时监控终端相关日志
            "adb logcat | grep -E \"(Terminal|Binary|CanaryAim|执行|命令|程序)\"",

            // 捕获错误和警告级别的日志
            "adb logcat *:W | grep \"$packageName\""
        )

        Log.i(TAG, "生成了 ${commands.size} 个 ADB 调试命令")
        return commands
    }

    /**
     * 打印 ADB 调试信息到日志
     */
    fun printDebugInfo(context: Context) {
        Log.i(TAG, "=== ADB 调试信息 ===")
        Log.i(TAG, "应用包名: ${getPackageName(context)}")
        Log.i(TAG, "应用版本: ${getAppVersion(context)}")
        Log.i(TAG, "设备信息: ${getDeviceInfo()}")
        Log.i(TAG, "Root 状态: ${if (rootAvailable()) "已获取" else "未获取"}")
        Log.i(TAG, "文件目录: ${context.filesDir.absolutePath}")
        Log.i(TAG, "原生库目录: ${context.applicationInfo.nativeLibraryDir}")
        Log.i(TAG, "=== 调试信息结束 ===")
    }

    /**
     * 获取应用版本信息
     */
    private fun getAppVersion(context: Context): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            "${packageInfo.versionName} (${packageInfo.versionCode})"
        } catch (e: Exception) {
            "未知版本"
        }
    }

    /**
     * 获取设备信息
     */
    private fun getDeviceInfo(): String {
        return "${Build.MANUFACTURER} ${Build.MODEL} (Android ${Build.VERSION.RELEASE}, API ${Build.VERSION.SDK_INT})"
    }
}

/**
 * 在应用启动时调用，用于输出调试信息
 */
fun initializeDebugLogging(context: Context) {
    Log.i("AppInit", "=== KernelSU Next 应用启动 ===")
    Log.i("AppInit", "初始化调试日志系统...")

    // 输出基本调试信息
    AdbDebugUtils.printDebugInfo(context)

    // 输出 ADB 命令建议
    val commands = AdbDebugUtils.getLogcatCommands(context)
    Log.i("AppInit", "推荐的 ADB 调试命令:")
    commands.forEachIndexed { index, command ->
        Log.i("AppInit", "${index + 1}. $command")
    }

    Log.i("AppInit", "调试日志系统初始化完成")
    Log.i("AppInit", "=== 应用启动完成 ===")
}
